import json
import zoneinfo
from datetime import datetime

GEN_AGENT_BASE_PROMPT_PATH = "llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/gen_agent.txt"
TRIAGE_AGENT_BASE_PROMPT_PATH = (
    "llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent.txt"
)

KNOWLEDGE_BASE_AGENT_BASE_PROMPT_PATH = (
    "llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/knowledge_base_agent.txt"
)


CHANNEL_GUIDELINE = "llm_app/pipeline/steps/agentic_framework/autogen/resources/default_config/channel_guidelines.json"


def load_system_message_template(path):
    try:
        with open(f"{path}", "r") as f:
            return f.read()
    except Exception as e:
        print(e)
        return ""


def read_config(path):
    try:
        with open(f"{path}", "r") as f:
            config = json.load(f)
            return config
    except Exception as e:
        print(e)
        return ""


def _get_combined_filler_messages(entities):
    all_messages = []
    for entity in entities:
        validation = entity.get("entity_validation_rule", {})
        responses = validation.get("response_template", [])
        all_messages.extend(responses)

    if not all_messages:
        return ""
    return "Example messages include:\n" + "\n".join(f'- "{msg}"' for msg in all_messages)


def _format_entity_description(index, entity):
    desc = f"{index + 1}. {entity['entity']}: {entity.get('description', '')}"
    rule = ""
    validation = entity.get("entity_validation_rule")
    if validation:
        rule_type = validation.get("type")
        if rule_type == "function":
            rule = (
                f"Ensure the customer input for '{entity['entity']}' "
                f"exists in the result returned by calling the function `{validation['function_name']}`."
            )
        elif rule_type == "enum":
            allowed_values = ", ".join(repr(v) for v in validation.get("valid_values", []))
            rule = f"Ensure the customer input for '{entity['entity']}' matches one of the allowed values: {allowed_values}."
        desc += f"\n   Validation Rule: {rule}"
    return desc


def _get_time_parameters(time_zone: str):
    timezone = zoneinfo.ZoneInfo(time_zone)
    current_time = datetime.now(timezone)
    parameter = {}
    parameter["today_date"] = str(current_time.strftime("%d"))
    parameter["today_month"] = str(current_time.strftime("%B"))
    parameter["today_year"] = str(current_time.strftime("%Y"))
    parameter["today_day"] = str(current_time.strftime("%A"))
    return parameter.copy()


def _get_channel_guideline_config_value(channel):
    config = read_config(CHANNEL_GUIDELINE)
    value = config.get("channel_response_guidelines", {}).get(channel, [])
    return "\n".join(f"- {cg}" for cg in value)


def _format_workflow_rules(workflows_data: dict, indent: str = "    "):
    return "\n".join(
        f"{indent}{workflow_id}:\n{indent}{indent}trigger rules:"
        + "".join(f"\n{indent}{indent}{indent}{rule}" for rule in rules)
        for workflow_id, rules in workflows_data.items()
    )


def _get_gen_agent_prompt_parameter(config: dict) -> str:
    # Entity Classification
    agent_bio = config.get("agent_bio", {})
    entities = agent_bio.get("entities", [])
    mandatory_entities, optional_entities, confirmation_entities = [], [], []
    all_entities_list = []
    example_prompts_list = []

    for i, e in enumerate(entities):
        if e.get("is_mandatory"):
            mandatory_entities.append(e["entity"])
        if not e.get("is_mandatory"):
            optional_entities.append(e["entity"])
        if e.get("is_confirmation"):
            confirmation_entities.append(e["entity"])
        all_entities_list.append(_format_entity_description(i, e))
        if e.get("response_template"):
            example_prompts_list.append(f"- For '{e['entity']}': {e.get('response_template')}")

    all_entities = "\n".join(all_entities_list)
    example_prompts_section = "\n".join(example_prompts_list)
    prompt_parameter = {
        "agent_title": agent_bio.get("agent_title"),
        "agent_role": agent_bio.get("agent_role"),
        "avatar_name": config.get("avatar_name"),
        "company_name": config.get("company_name"),
        "use_case_domain": config.get("use_case_domain"),
        "traits": ", ".join(agent_bio.get("personality").get("traits")),
        "style": ", ".join(agent_bio.get("personality").get("style")),
        "mandatory_entities": (", ".join(mandatory_entities) if mandatory_entities else "None"),
        "optional_entities": (", ".join(optional_entities) if optional_entities else "None"),
        "confirmation_entities": (", ".join(confirmation_entities) if confirmation_entities else "None"),
        "all_entities": all_entities,
        "filler_messages": _get_combined_filler_messages(entities),
        "example_prompts_section": example_prompts_section,
        "conditional_actions": "\n- ".join(
            f"{a['condition']}, {a['action']}" for a in agent_bio.get("conditional_actions", [])
        ),
        "important_guidelines": "\n".join(f"- {g}" for g in agent_bio.get("important_guidelines", [])),
        "initiation_guideline": agent_bio.get("initiation_guideline"),
        "llm_context": "\n    ".join(f"{k}: {v}" for k, v in config["llm_context"].items()),
        "channel_specific_guidelines": _get_channel_guideline_config_value(config.get("channel")),
    }
    prompt_parameter.update(_get_time_parameters(config.get("time_zone", "UTC")))
    return prompt_parameter.copy()


def _get_triage_agent_prompt_parameter(config: dict) -> str:
    workflows = _format_workflow_rules(config.get("workflows", {}))
    prompt_parameter = {
        "use_case_domain": config.get("use_case_domain"),
        "workflows": workflows,
    }
    return prompt_parameter.copy()


def _get_gen_agent_system_prompt(config: dict) -> str:
    prompt_parameter = _get_gen_agent_prompt_parameter(config)
    prompt = load_system_message_template(GEN_AGENT_BASE_PROMPT_PATH)
    system_prompt = prompt.format(**prompt_parameter)
    return system_prompt


def _get_triage_agent_system_prompt(config: dict) -> str:
    prompt_parameter = _get_triage_agent_prompt_parameter(config)
    prompt = load_system_message_template(TRIAGE_AGENT_BASE_PROMPT_PATH)
    system_prompt = prompt.format(**prompt_parameter)
    return system_prompt


def _get_kb_agent_prompt_parameter(config: dict) -> str:
    instructions = "\n".join([instruction.strip() for instruction in config.get("instructions", [])])
    knowledge_snippets = "\n\n".join(config.get("knowledge_snippets", []))

    prompt_parameter = {
        "avatar_name": config.get("avatar_name"),
        "company_name": config.get("company_name"),
        "use_case_domain": config.get("use_case_domain"),
        "knowledge_snippets": knowledge_snippets,
        "instructions": instructions,
    }
    return prompt_parameter.copy()


def _get_kb_agent_system_prompt(config: dict) -> str:
    prompt_parameter = _get_kb_agent_prompt_parameter(config)
    prompt = load_system_message_template(KNOWLEDGE_BASE_AGENT_BASE_PROMPT_PATH)
    system_prompt = prompt.format(**prompt_parameter)
    return system_prompt
