import types
from autogen_core import TypeSubscription
from autogen_core.models import SystemMessage
from autogen_core.tools import FunctionTool

from llm_app.pipeline.steps.agentic_framework.autogen.interfaces.agent_base import AIAgent

from llm_app.pipeline.steps.agentic_framework.autogen.utils.prompt_generation import _get_triage_agent_system_prompt

class TriageAgentWrapper:
    def __init__(
        self,
        *,
        config: dict,
        runtime,
        model_client,
        response_queue,
        user_topic_type,
        toolset: list = None,
        delegate_tools: list = None,
    ):
        self.config = config
        self.runtime = runtime
        self.model_client = model_client
        self.response_queue = response_queue
        self.user_topic_type = user_topic_type
        self.toolset = toolset or []
        self.delegate_tools = delegate_tools or []

        self.agent_topic_type = "Triage_Agent"
        system_prompt = _get_triage_agent_system_prompt(config.copy())
        self.system_message = SystemMessage(content=system_prompt)
        self.description = "Responsible for routing the customer to the appropriate support agent based on the issue category."

        # DEBUG: Print triage agent initialization
        print(f"\n🎯 TRIAGE AGENT DEBUG - INITIALIZATION")
        print(f"📋 Use Case Domain: {config.get('use_case_domain', 'Unknown')}")
        print(f"🏢 Company: {config.get('company_name', 'Unknown')}")
        print(f"👤 Avatar: {config.get('avatar_name', 'Unknown')}")
        print(f"🔧 Available Tools: {len(self.toolset)}")
        print(f"🔄 Delegate Tools: {len(self.delegate_tools)}")
        print(f"📝 Workflows Available: {list(config.get('workflows', {}).keys())}")
        print(f"📄 System Prompt Length: {len(system_prompt)} chars")
        print(f"📄 System Prompt Preview: {system_prompt[:200]}...")
        print("=" * 80)

    async def create(self):
        agent_type = await AIAgent.register(
            self.runtime,
            type=self.agent_topic_type,
            factory=lambda: AIAgent(
                description=self.description,
                system_message=self.system_message,
                model_client=self.model_client,
                tools=self.toolset,
                delegate_tools=self.delegate_tools,
                agent_topic_type=self.agent_topic_type,
                user_topic_type=self.user_topic_type,
                response_queue=self.response_queue,
            ),
        )

        await self.runtime.add_subscription(
            TypeSubscription(topic_type=self.agent_topic_type, agent_type=agent_type.type)
        )
        return agent_type

    def get_delegate_tool(self):
        agent_role = "Responsible for routing the customer to the appropriate support agent based on the issue category."
        agent_name = "Triage_Agent"
        agent_topic = self.agent_topic_type

        # Generate a safe function name from agent_name
        func_name = f"route_to_{agent_name.lower().replace(' ', '_')}"

        # Define a closure-compatible function
        def _route_fn() -> str:
            return agent_topic

        # Create a dynamic function with the proper closure
        code = _route_fn.__code__
        globals_dict = globals()
        name = func_name
        argdefs = _route_fn.__defaults__
        closure = _route_fn.__closure__  # This is what was missing

        # Construct the new function
        route_fn = types.FunctionType(code, globals_dict, name, argdefs, closure)

        return FunctionTool(
            route_fn,
            description=(
                f"Use this tool if the customer is discussing anything related to: {agent_role}.\n"
                f"This will route the conversation to **{agent_name}**."
            ),
        )
